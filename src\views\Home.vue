<template>
    <v-scale-screen width="7552" height="960">
        <div class="home-container">
            <!-- 头部组件 -->
            <HeaderComponent
                @project-change="handleProjectChange"
            />

            <div class="main-area">
                <div class="content-area">
                    <Transition name="fade" mode="out-in">
                        <component :is="currentView" @switchView="handleSwitchView">

                        </component>
                    </Transition>
                </div>
            </div>
            
        </div>
    </v-scale-screen>
</template>
<script setup>
import { shallowRef } from 'vue'
import VScaleScreen from 'v-scale-screen'
import HeaderComponent from '../components/layout/HeaderComponent.vue'
import Dashboard from './Dashboard.vue'
import GridView from './GridView.vue'

// 定义emit事件，用于向App.vue传递事件
const emit = defineEmits(['project-change'])

const currentView = shallowRef(GridView)

const handleSwitchView = (view) => {
    if (view === 'dashboard') {
        currentView.value = Dashboard
    }
}



// 处理项目切换
const handleProjectChange = (projectId) => {
    console.log('项目切换到:', projectId)
    // 将事件传递给App.vue
    emit('project-change', projectId)

    // 这里可以根据项目切换来更新页面内容或执行其他逻辑
}
</script>

<style lang="less" scoped>
// v-scale-screen 组件不阻挡交互
:deep(.v-screen-box) {
    pointer-events: none !important;
}

.home-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: transparent;
    pointer-events: none; // 不阻挡Three.js交互

    // 头部组件区域需要交互
    :deep(.header-container) {
        pointer-events: auto;
    }

    .main-area {
        flex: 1;
        min-height: 0;
        overflow: hidden;
        pointer-events: none; // 不阻挡Three.js交互
    }

    .content-area {
        height: 100%;
        position: relative;
        background: transparent;
        pointer-events: none; // 不阻挡Three.js交互

        // 只有特定的UI组件需要交互
        :deep(.el-button),
        :deep(.el-select),
        :deep(.el-input),
        :deep(.el-dialog),
        :deep(.chart-container),
        :deep(.data-card),
        :deep(.statistics-card) {
            pointer-events: auto;
        }
    }
}

// 淡入淡出动画
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}
</style>